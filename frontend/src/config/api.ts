export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';
export const WS_BASE_URL = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8000';

export const API_ENDPOINTS = {
  // Auth endpoints
  REGISTER: '/api/v1/auth/register',
  LOGIN: '/api/v1/auth/login',
  LOGOUT: '/api/v1/auth/logout',
  ME: '/api/v1/auth/me',
  
  // User endpoints
  USERS: '/api/v1/users',
  USER_BY_ID: (id: number) => `/api/v1/users/${id}`,
  USER_BY_USERNAME: (username: string) => `/api/v1/users/username/${username}`,
  
  // Message endpoints
  MESSAGES: '/api/v1/messages',
  DIRECT_MESSAGES: (userId: number) => `/api/v1/messages/direct/${userId}`,
  ROOM_MESSAGES: (roomId: number) => `/api/v1/messages/room/${roomId}`,
  
  // Room endpoints
  ROOMS: '/api/v1/rooms',
  ROOM_BY_ID: (id: number) => `/api/v1/rooms/${id}`,
  JOIN_ROOM: (id: number) => `/api/v1/rooms/${id}/join`,
  LEAVE_ROOM: (id: number) => `/api/v1/rooms/${id}/leave`,
  ROOM_MEMBERS: (id: number) => `/api/v1/rooms/${id}/members`,
};

export const WS_ENDPOINTS = {
  CHAT: '/ws',
};
