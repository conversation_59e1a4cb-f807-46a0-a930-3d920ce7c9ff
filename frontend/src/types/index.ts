// User types
export interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  is_active: boolean;
  is_online: boolean;
  created_at: string;
  updated_at: string;
}

export interface UserCreate {
  username: string;
  email: string;
  password: string;
  full_name?: string;
}

export interface UserLogin {
  username: string;
  password: string;
}

export interface Token {
  access_token: string;
  token_type: string;
}

// Message types
export interface Message {
  id: number;
  content: string;
  sender_id: number;
  recipient_id?: number;
  room_id?: number;
  message_type: 'DIRECT' | 'ROOM';
  status: 'SENT' | 'DELIVERED' | 'READ';
  created_at: string;
  updated_at: string;
  sender?: User;
  recipient?: User;
}

export interface MessageCreate {
  content: string;
  recipient_id?: number;
  room_id?: number;
  message_type: 'DIRECT' | 'ROOM';
}

// Room types
export interface Room {
  id: number;
  name: string;
  description?: string;
  room_type: 'PUBLIC' | 'PRIVATE';
  created_by: number;
  created_at: string;
  updated_at: string;
  creator?: User;
  members?: User[];
}

export interface RoomCreate {
  name: string;
  description?: string;
  room_type: 'PUBLIC' | 'PRIVATE';
}

// WebSocket types
export interface WebSocketMessage {
  type: string;
  data: any;
}

export interface ChatMessage {
  content: string;
  recipient_id?: number;
  room_id?: number;
  message_type: 'DIRECT' | 'ROOM';
}

// API Response types
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface ApiError {
  detail: string;
}
