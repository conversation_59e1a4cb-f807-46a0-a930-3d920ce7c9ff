import React, { createContext, useContext, useEffect, useState } from 'react';
import { User, UserLogin, UserCreate, Token } from '../types';
import { apiService } from '../services/api';
import { API_ENDPOINTS } from '../config/api';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  login: (credentials: UserLogin) => Promise<void>;
  register: (userData: UserCreate) => Promise<void>;
  logout: () => void;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const token = apiService.getAuthToken();
    const savedUser = localStorage.getItem('user');
    
    if (token && savedUser) {
      try {
        setUser(JSON.parse(savedUser));
      } catch (error) {
        console.error('Error parsing saved user:', error);
        apiService.removeAuthToken();
      }
    }
    
    setLoading(false);
  }, []);

  const login = async (credentials: UserLogin) => {
    try {
      const tokenData: Token = await apiService.post(API_ENDPOINTS.LOGIN, credentials);
      apiService.setAuthToken(tokenData.access_token);
      
      // Get user info
      const userData: User = await apiService.get(API_ENDPOINTS.ME);
      setUser(userData);
      localStorage.setItem('user', JSON.stringify(userData));
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };

  const register = async (userData: UserCreate) => {
    try {
      const newUser: User = await apiService.post(API_ENDPOINTS.REGISTER, userData);
      
      // Auto-login after registration
      await login({ username: userData.username, password: userData.password });
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  };

  const logout = () => {
    apiService.removeAuthToken();
    setUser(null);
    // Optionally call logout endpoint
    apiService.post(API_ENDPOINTS.LOGOUT).catch(console.error);
  };

  const value: AuthContextType = {
    user,
    loading,
    login,
    register,
    logout,
    isAuthenticated: !!user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
