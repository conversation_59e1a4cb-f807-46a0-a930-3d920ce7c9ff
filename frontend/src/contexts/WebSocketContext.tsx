import React, { createContext, useContext, useEffect, useState } from 'react';
import { websocketService } from '../services/websocket';
import { useAuth } from './AuthContext';
import { Message } from '../types';

interface WebSocketContextType {
  isConnected: boolean;
  sendMessage: (content: string, recipientId?: number, roomId?: number) => void;
  joinRoom: (roomId: number) => void;
  leaveRoom: (roomId: number) => void;
  onNewMessage: (handler: (message: Message) => void) => () => void;
  onUserStatusChange: (handler: (data: any) => void) => () => void;
}

const WebSocketContext = createContext<WebSocketContextType | undefined>(undefined);

export const useWebSocket = () => {
  const context = useContext(WebSocketContext);
  if (context === undefined) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};

interface WebSocketProviderProps {
  children: React.ReactNode;
}

export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);
  const { user, isAuthenticated } = useAuth();
  const [messageHandlers, setMessageHandlers] = useState<((message: Message) => void)[]>([]);
  const [statusHandlers, setStatusHandlers] = useState<((data: any) => void)[]>([]);

  useEffect(() => {
    if (isAuthenticated && user) {
      const token = localStorage.getItem('access_token');
      if (token) {
        websocketService.connect(token);

        const unsubscribeConnect = websocketService.onConnect(() => {
          setIsConnected(true);
        });

        const unsubscribeDisconnect = websocketService.onDisconnect(() => {
          setIsConnected(false);
        });

        const unsubscribeMessage = websocketService.onMessage((data) => {
          console.log('WebSocket message received:', data);
          
          switch (data.type) {
            case 'new_message':
              messageHandlers.forEach(handler => handler(data.data));
              break;
            case 'user_status':
              statusHandlers.forEach(handler => handler(data.data));
              break;
            case 'room_update':
              // Handle room updates
              break;
            default:
              console.log('Unknown message type:', data.type);
          }
        });

        return () => {
          unsubscribeConnect();
          unsubscribeDisconnect();
          unsubscribeMessage();
          websocketService.disconnect();
        };
      }
    } else {
      websocketService.disconnect();
      setIsConnected(false);
    }
  }, [isAuthenticated, user, messageHandlers, statusHandlers]);

  const sendMessage = (content: string, recipientId?: number, roomId?: number) => {
    websocketService.sendChatMessage(content, recipientId, roomId);
  };

  const joinRoom = (roomId: number) => {
    websocketService.joinRoom(roomId);
  };

  const leaveRoom = (roomId: number) => {
    websocketService.leaveRoom(roomId);
  };

  const onNewMessage = (handler: (message: Message) => void) => {
    setMessageHandlers(prev => [...prev, handler]);
    return () => {
      setMessageHandlers(prev => prev.filter(h => h !== handler));
    };
  };

  const onUserStatusChange = (handler: (data: any) => void) => {
    setStatusHandlers(prev => [...prev, handler]);
    return () => {
      setStatusHandlers(prev => prev.filter(h => h !== handler));
    };
  };

  const value: WebSocketContextType = {
    isConnected,
    sendMessage,
    joinRoom,
    leaveRoom,
    onNewMessage,
    onUserStatusChange,
  };

  return <WebSocketContext.Provider value={value}>{children}</WebSocketContext.Provider>;
};
