import React, { useEffect, useState, useRef } from 'react';
import { Message, MessageCreate } from '../../types';
import { apiService } from '../../services/api';
import { API_ENDPOINTS } from '../../config/api';
import { useAuth } from '../../contexts/AuthContext';
import { useWebSocket } from '../../contexts/WebSocketContext';
import { Send, MoreVertical, Wifi, WifiOff } from 'lucide-react';

interface ChatAreaProps {
  chatType: 'user' | 'room';
  chatId: number;
  chatName: string;
}

const ChatArea: React.FC<ChatAreaProps> = ({ chatType, chatId, chatName }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [newMessage, setNewMessage] = useState('');
  const [sending, setSending] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { user } = useAuth();
  const { isConnected, sendMessage: sendWebSocketMessage, onNewMessage } = useWebSocket();

  useEffect(() => {
    fetchMessages();
  }, [chatType, chatId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Listen for new messages via WebSocket
    const unsubscribe = onNewMessage((message: Message) => {
      // Only add message if it's for the current chat
      const isRelevantMessage =
        (chatType === 'user' &&
         ((message.sender_id === chatId && message.recipient_id === user?.id) ||
          (message.sender_id === user?.id && message.recipient_id === chatId))) ||
        (chatType === 'room' && message.room_id === chatId);

      if (isRelevantMessage) {
        setMessages(prev => [...prev, message]);
      }
    });

    return unsubscribe;
  }, [chatType, chatId, user?.id, onNewMessage]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const fetchMessages = async () => {
    try {
      setLoading(true);
      const endpoint = chatType === 'user' 
        ? API_ENDPOINTS.DIRECT_MESSAGES(chatId)
        : API_ENDPOINTS.ROOM_MESSAGES(chatId);
      
      const response = await apiService.get(endpoint, {
        page: 1,
        size: 100,
      });
      setMessages(response.items || []);
    } catch (error: any) {
      console.error('Error fetching messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() || sending) return;

    const content = newMessage.trim();
    setNewMessage('');
    setSending(true);

    try {
      if (isConnected) {
        // Send via WebSocket for real-time delivery
        sendWebSocketMessage(
          content,
          chatType === 'user' ? chatId : undefined,
          chatType === 'room' ? chatId : undefined
        );
      } else {
        // Fallback to HTTP API if WebSocket is not connected
        const messageData: MessageCreate = {
          content,
          message_type: chatType === 'user' ? 'DIRECT' : 'ROOM',
          ...(chatType === 'user' ? { recipient_id: chatId } : { room_id: chatId }),
        };

        const message = await apiService.post(API_ENDPOINTS.MESSAGES, messageData);
        setMessages(prev => [...prev, message]);
      }
    } catch (error: any) {
      console.error('Error sending message:', error);
      // Restore message on error
      setNewMessage(content);
    } finally {
      setSending(false);
    }
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('ar-SA', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return 'اليوم';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'أمس';
    } else {
      return date.toLocaleDateString('ar-SA');
    }
  };

  if (loading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center">
              <span className="text-white font-semibold">
                {chatName.charAt(0).toUpperCase()}
              </span>
            </div>
            <div>
              <h3 className="font-semibold text-gray-900">{chatName}</h3>
              <div className="flex items-center space-x-2">
                <p className="text-sm text-gray-500">
                  {chatType === 'user' ? 'محادثة مباشرة' : 'غرفة دردشة'}
                </p>
                <div className={`flex items-center space-x-1 text-xs ${
                  isConnected ? 'text-green-600' : 'text-red-600'
                }`}>
                  {isConnected ? (
                    <Wifi className="w-3 h-3" />
                  ) : (
                    <WifiOff className="w-3 h-3" />
                  )}
                  <span>{isConnected ? 'متصل' : 'غير متصل'}</span>
                </div>
              </div>
            </div>
          </div>
          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
            <MoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>لا توجد رسائل بعد</p>
            <p className="text-sm">ابدأ المحادثة بإرسال رسالة</p>
          </div>
        ) : (
          messages.map((message, index) => {
            const isOwnMessage = message.sender_id === user?.id;
            const showDate = index === 0 || 
              formatDate(message.created_at) !== formatDate(messages[index - 1].created_at);

            return (
              <div key={message.id}>
                {showDate && (
                  <div className="text-center text-xs text-gray-500 my-4">
                    {formatDate(message.created_at)}
                  </div>
                )}
                <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'}`}>
                  <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    isOwnMessage
                      ? 'bg-primary-500 text-white'
                      : 'bg-gray-200 text-gray-900'
                  }`}>
                    {!isOwnMessage && chatType === 'room' && (
                      <p className="text-xs font-semibold mb-1 opacity-75">
                        {message.sender?.username}
                      </p>
                    )}
                    <p className="text-sm">{message.content}</p>
                    <p className={`text-xs mt-1 ${
                      isOwnMessage ? 'text-primary-100' : 'text-gray-500'
                    }`}>
                      {formatTime(message.created_at)}
                    </p>
                  </div>
                </div>
              </div>
            );
          })
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-gray-200 bg-white">
        <form onSubmit={handleSendMessage} className="flex space-x-3">
          <input
            type="text"
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="اكتب رسالة..."
            className="input flex-1"
            disabled={sending}
          />
          <button
            type="submit"
            disabled={!newMessage.trim() || sending}
            className="btn btn-primary px-4 py-2 flex items-center justify-center"
          >
            <Send className="w-4 h-4" />
          </button>
        </form>
      </div>
    </div>
  );
};

export default ChatArea;
