import React, { useEffect, useState } from 'react';
import { Room, RoomCreate } from '../../types';
import { apiService } from '../../services/api';
import { API_ENDPOINTS } from '../../config/api';
import { Search, Plus, Lock, Globe } from 'lucide-react';

interface RoomsListProps {
  onSelectRoom: (room: Room) => void;
}

const RoomsList: React.FC<RoomsListProps> = ({ onSelectRoom }) => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState('');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newRoom, setNewRoom] = useState<RoomCreate>({
    name: '',
    description: '',
    room_type: 'PUBLIC',
  });

  useEffect(() => {
    fetchRooms();
  }, []);

  const fetchRooms = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(API_ENDPOINTS.ROOMS, {
        page: 1,
        size: 100,
        search: searchTerm || undefined,
      });
      setRooms(response.items || []);
    } catch (error: any) {
      setError('فشل في تحميل قائمة الغرف');
      console.error('Error fetching rooms:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchRooms();
  };

  const handleCreateRoom = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const room = await apiService.post(API_ENDPOINTS.ROOMS, newRoom);
      setRooms([room, ...rooms]);
      setShowCreateForm(false);
      setNewRoom({ name: '', description: '', room_type: 'PUBLIC' });
    } catch (error: any) {
      console.error('Error creating room:', error);
    }
  };

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mt-1"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="text-center text-red-600">
          <p>{error}</p>
          <button
            onClick={fetchRooms}
            className="mt-2 text-sm text-primary-600 hover:text-primary-700"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      {/* Search and Create */}
      <div className="mb-4 space-y-3">
        <form onSubmit={handleSearchSubmit}>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <input
              type="text"
              placeholder="البحث عن غرفة..."
              value={searchTerm}
              onChange={handleSearch}
              className="input pl-10 w-full"
            />
          </div>
        </form>
        
        <button
          onClick={() => setShowCreateForm(!showCreateForm)}
          className="btn btn-primary w-full flex items-center justify-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>إنشاء غرفة جديدة</span>
        </button>
      </div>

      {/* Create Room Form */}
      {showCreateForm && (
        <form onSubmit={handleCreateRoom} className="mb-4 p-3 bg-gray-50 rounded-lg space-y-3">
          <input
            type="text"
            placeholder="اسم الغرفة"
            value={newRoom.name}
            onChange={(e) => setNewRoom({ ...newRoom, name: e.target.value })}
            className="input w-full"
            required
          />
          <input
            type="text"
            placeholder="وصف الغرفة (اختياري)"
            value={newRoom.description}
            onChange={(e) => setNewRoom({ ...newRoom, description: e.target.value })}
            className="input w-full"
          />
          <select
            value={newRoom.room_type}
            onChange={(e) => setNewRoom({ ...newRoom, room_type: e.target.value as 'PUBLIC' | 'PRIVATE' })}
            className="input w-full"
          >
            <option value="PUBLIC">عامة</option>
            <option value="PRIVATE">خاصة</option>
          </select>
          <div className="flex space-x-2">
            <button type="submit" className="btn btn-primary flex-1">
              إنشاء
            </button>
            <button
              type="button"
              onClick={() => setShowCreateForm(false)}
              className="btn btn-outline flex-1"
            >
              إلغاء
            </button>
          </div>
        </form>
      )}

      {/* Rooms List */}
      <div className="space-y-2">
        {rooms.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>لا توجد غرف</p>
          </div>
        ) : (
          rooms.map((room) => (
            <button
              key={room.id}
              onClick={() => onSelectRoom(room)}
              className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                {room.room_type === 'PRIVATE' ? (
                  <Lock className="w-5 h-5 text-primary-600" />
                ) : (
                  <Globe className="w-5 h-5 text-primary-600" />
                )}
              </div>
              <div className="flex-1 text-left">
                <h4 className="font-medium text-gray-900">{room.name}</h4>
                <p className="text-sm text-gray-500">
                  {room.description || (room.room_type === 'PRIVATE' ? 'غرفة خاصة' : 'غرفة عامة')}
                </p>
              </div>
            </button>
          ))
        )}
      </div>
    </div>
  );
};

export default RoomsList;
