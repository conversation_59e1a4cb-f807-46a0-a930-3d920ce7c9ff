import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { LogOut, Settings, Users, MessageSquare } from 'lucide-react';
import UsersList from './UsersList';
import RoomsList from './RoomsList';
import Chat<PERSON>rea from './ChatArea';

const ChatLayout: React.FC = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState<'users' | 'rooms'>('users');
  const [selectedChat, setSelectedChat] = useState<{
    type: 'user' | 'room';
    id: number;
    name: string;
  } | null>(null);

  const handleLogout = () => {
    logout();
  };

  return (
    <div className="h-screen flex bg-gray-100">
      {/* Sidebar */}
      <div className="w-80 bg-white border-r border-gray-200 flex flex-col">
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center">
                <span className="text-white font-semibold">
                  {user?.username.charAt(0).toUpperCase()}
                </span>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">{user?.username}</h3>
                <p className="text-sm text-gray-500">متصل</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                title="الإعدادات"
              >
                <Settings className="w-5 h-5" />
              </button>
              <button
                onClick={handleLogout}
                className="p-2 text-gray-400 hover:text-red-600 rounded-lg hover:bg-gray-100"
                title="تسجيل الخروج"
              >
                <LogOut className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('users')}
            className={`flex-1 py-3 px-4 text-sm font-medium flex items-center justify-center space-x-2 ${
              activeTab === 'users'
                ? 'text-primary-600 border-b-2 border-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <Users className="w-4 h-4" />
            <span>المستخدمين</span>
          </button>
          <button
            onClick={() => setActiveTab('rooms')}
            className={`flex-1 py-3 px-4 text-sm font-medium flex items-center justify-center space-x-2 ${
              activeTab === 'rooms'
                ? 'text-primary-600 border-b-2 border-primary-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            <MessageSquare className="w-4 h-4" />
            <span>الغرف</span>
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          {activeTab === 'users' ? (
            <UsersList onSelectUser={(user) => setSelectedChat({ type: 'user', id: user.id, name: user.username })} />
          ) : (
            <RoomsList onSelectRoom={(room) => setSelectedChat({ type: 'room', id: room.id, name: room.name })} />
          )}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {selectedChat ? (
          <ChatArea
            chatType={selectedChat.type}
            chatId={selectedChat.id}
            chatName={selectedChat.name}
          />
        ) : (
          <div className="flex-1 flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <MessageSquare className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">مرحباً بك في تطبيق الدردشة</h3>
              <p className="text-gray-500">اختر مستخدماً أو غرفة لبدء المحادثة</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatLayout;
