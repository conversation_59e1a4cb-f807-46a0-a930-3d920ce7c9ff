import React, { useEffect, useState } from 'react';
import { User } from '../../types';
import { apiService } from '../../services/api';
import { API_ENDPOINTS } from '../../config/api';
import { Search, Circle } from 'lucide-react';

interface UsersListProps {
  onSelectUser: (user: User) => void;
}

const UsersList: React.FC<UsersListProps> = ({ onSelectUser }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [error, setError] = useState('');

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await apiService.get(API_ENDPOINTS.USERS, {
        page: 1,
        size: 100,
        search: searchTerm || undefined,
      });
      setUsers(response.items || []);
    } catch (error: any) {
      setError('فشل في تحميل قائمة المستخدمين');
      console.error('Error fetching users:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    fetchUsers();
  };

  if (loading) {
    return (
      <div className="p-4">
        <div className="animate-pulse space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mt-1"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4">
        <div className="text-center text-red-600">
          <p>{error}</p>
          <button
            onClick={fetchUsers}
            className="mt-2 text-sm text-primary-600 hover:text-primary-700"
          >
            إعادة المحاولة
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      {/* Search */}
      <form onSubmit={handleSearchSubmit} className="mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="البحث عن مستخدم..."
            value={searchTerm}
            onChange={handleSearch}
            className="input pl-10 w-full"
          />
        </div>
      </form>

      {/* Users List */}
      <div className="space-y-2">
        {users.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <p>لا توجد مستخدمين</p>
          </div>
        ) : (
          users.map((user) => (
            <button
              key={user.id}
              onClick={() => onSelectUser(user)}
              className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="relative">
                <div className="w-10 h-10 bg-primary-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-semibold">
                    {user.username.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                  user.is_online ? 'bg-green-500' : 'bg-gray-400'
                }`}>
                  <Circle className="w-full h-full" />
                </div>
              </div>
              <div className="flex-1 text-left">
                <h4 className="font-medium text-gray-900">{user.username}</h4>
                <p className="text-sm text-gray-500">
                  {user.full_name || user.email}
                </p>
              </div>
              <div className="text-xs text-gray-400">
                {user.is_online ? 'متصل' : 'غير متصل'}
              </div>
            </button>
          ))
        )}
      </div>
    </div>
  );
};

export default UsersList;
